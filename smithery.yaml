startCommand:
  type: stdio
  configSchema:
    type: object
    required: ["apiKey"]
    properties:
      apiKey:
        type: string
        title: "The Cat API Key"
        description: "Get your API key from https://thecatapi.com/signup"
      timeout:
        type: number
        title: "API Timeout (seconds)"
        default: 30.0
        minimum: 5
        maximum: 60
  commandFunction: |-
    (config) => ({
      "command": "python",
      "args": ["cat_api_server.py"],
      "env": {
        "CAT_API_KEY": config.apiKey,
        "CAT_API_TIMEOUT": config.timeout ? config.timeout.toString() : "30.0"
      }
    })