"""
Configuration module for The Cat API MCP Server
"""

import os
from typing import Optional
from dataclasses import dataclass

@dataclass
class CatAPIConfig:
    """Configuration settings for The Cat API MCP Server"""
    
    # API Configuration
    api_key: str
    base_url: str = "https://api.thecatapi.com/v1"
    timeout: float = 30.0
    
    # Server Configuration
    server_name: str = "TheCatAPI Server"
    server_description: str = "A comprehensive MCP server for The Cat API"
    
    # Rate Limiting
    max_requests_per_minute: int = 100
    max_concurrent_requests: int = 10
    
    # Caching (if implemented)
    cache_ttl_seconds: int = 300  # 5 minutes
    enable_cache: bool = False
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @classmethod
    def from_env(cls) -> 'CatAPIConfig':
        """Create configuration from environment variables"""
        api_key = os.getenv("CAT_API_KEY")
        if not api_key:
            raise ValueError(
                "CAT_API_KEY environment variable is required. "
                "Get your API key from https://thecatapi.com/signup"
            )
        
        return cls(
            api_key=api_key,
            base_url=os.getenv("CAT_API_BASE_URL", cls.base_url),
            timeout=float(os.getenv("CAT_API_TIMEOUT", cls.timeout)),
            server_name=os.getenv("MCP_SERVER_NAME", cls.server_name),
            server_description=os.getenv("MCP_SERVER_DESCRIPTION", cls.server_description),
            max_requests_per_minute=int(os.getenv("MAX_REQUESTS_PER_MINUTE", cls.max_requests_per_minute)),
            max_concurrent_requests=int(os.getenv("MAX_CONCURRENT_REQUESTS", cls.max_concurrent_requests)),
            cache_ttl_seconds=int(os.getenv("CACHE_TTL_SECONDS", cls.cache_ttl_seconds)),
            enable_cache=os.getenv("ENABLE_CACHE", "false").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", cls.log_level),
            log_format=os.getenv("LOG_FORMAT", cls.log_format)
        )

# Breed ID mappings for easy reference
BREED_IDS = {
    "abyssinian": "abys",
    "aegean": "aege",
    "american_bobtail": "abob",
    "american_curl": "acur",
    "american_shorthair": "asho",
    "american_wirehair": "awir",
    "arabian_mau": "amau",
    "australian_mist": "amis",
    "balinese": "bali",
    "bambino": "bamb",
    "bengal": "beng",
    "birman": "birm",
    "bombay": "bmbl",
    "british_longhair": "bslo",
    "british_shorthair": "bsho",
    "burmese": "bure",
    "burmilla": "buri",
    "california_spangled": "cspa",
    "chantilly_tiffany": "ctif",
    "chartreux": "char",
    "chausie": "chau",
    "cheetoh": "chee",
    "colorpoint_shorthair": "csho",
    "cornish_rex": "crex",
    "cymric": "cymr",
    "cyprus": "cypr",
    "devon_rex": "drex",
    "donskoy": "dons",
    "dragon_li": "lihu",
    "egyptian_mau": "emau",
    "european_burmese": "ebur",
    "exotic_shorthair": "esho",
    "havana_brown": "hbro",
    "himalayan": "hima",
    "japanese_bobtail": "jbob",
    "javanese": "java",
    "khao_manee": "khao",
    "korat": "kora",
    "kurilian": "kuri",
    "laperm": "lape",
    "maine_coon": "mcoo",
    "malayan": "mala",
    "manx": "manx",
    "mau": "mau",
    "munchkin": "munc",
    "nebelung": "nebe",
    "norwegian_forest_cat": "norw",
    "ocicat": "ocic",
    "oriental": "orie",
    "persian": "pers",
    "pixie_bob": "pixi",
    "ragamuffin": "raga",
    "ragdoll": "ragd",
    "russian_blue": "rblu",
    "savannah": "sava",
    "scottish_fold": "sfol",
    "selkirk_rex": "srex",
    "siamese": "siam",
    "siberian": "sibe",
    "singapura": "sing",
    "snowshoe": "snow",
    "somali": "soma",
    "sphynx": "sphy",
    "tonkinese": "tonk",
    "toyger": "toyg",
    "turkish_angora": "tang",
    "turkish_van": "tvan"
}

# Category ID mappings
CATEGORY_IDS = {
    "hats": 1,
    "space": 2,
    "funny": 3,
    "sunglasses": 4,
    "boxes": 5,
    "caturday": 6,
    "ties": 7,
    "dream": 8,
    "kittens": 9,
    "sinks": 10,
    "clothes": 11,
    "pose": 12,
    "source": 13
}
