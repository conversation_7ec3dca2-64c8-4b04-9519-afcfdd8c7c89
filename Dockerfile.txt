# Use Python 3.11 slim image for optimal size and security
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user for security
RUN groupadd -r catapi && useradd -r -g catapi catapi
RUN chown -R catapi:catapi /app
USER catapi

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import httpx; import os; httpx.get('https://api.thecatapi.com/v1/images/search?limit=1', headers={'x-api-key': os.getenv('CAT_API_KEY', '')}).raise_for_status()" || exit 1

# Expose port (if running HTTP transport)
EXPOSE 8080

# Default command
CMD ["python", "cat_api_server.py"]
