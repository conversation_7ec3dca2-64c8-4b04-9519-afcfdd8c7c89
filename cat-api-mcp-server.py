#!/usr/bin/env python3
"""
The Cat API MCP Server

A production-ready MCP server that provides tools for interacting with The Cat API.
Includes cat image search, breed information, categories, and more.
"""

import os
import asyncio
import httpx
from typing import List, Dict, Any, Optional, Union
from fastmcp import FastMCP
from fastmcp.exceptions import ToolError
from pydantic import Field
from typing_extensions import Annotated
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP(
    name="TheCatAPI Server",
    description="A comprehensive MCP server for The Cat API providing cat images, breed information, and more"
)

# Configuration
CAT_API_BASE_URL = "https://api.thecatapi.com/v1"
API_KEY = os.getenv("CAT_API_KEY", "live_pQYkgzG60lG2Me2D05xj3fpT74V0Ni0EFHeJKe54tslXaF70tOtPi4vytdWReJn5")

# HTTP client for API requests
async def get_http_client() -> httpx.AsyncClient:
    """Get configured HTTP client with API key."""
    headers = {
        "x-api-key": API_KEY,
        "Content-Type": "application/json"
    }
    return httpx.AsyncClient(headers=headers, timeout=30.0)

@mcp.tool()
async def search_cat_images(
    limit: Annotated[int, Field(description="Number of images to return (1-100)", ge=1, le=100)] = 1,
    page: Annotated[int, Field(description="Page number for pagination", ge=0)] = 0,
    order: Annotated[str, Field(description="Order of results")] = "RAND",
    has_breeds: Annotated[bool, Field(description="Only return images with breed information")] = False,
    breed_ids: Annotated[Optional[str], Field(description="Comma-separated breed IDs to filter by")] = None,
    category_ids: Annotated[Optional[str], Field(description="Comma-separated category IDs to filter by")] = None,
    sub_id: Annotated[Optional[str], Field(description="Filter by sub_id used when uploading")] = None
) -> Dict[str, Any]:
    """
    Search for cat images with various filtering options.
    
    Returns a list of cat images with metadata including breed information when available.
    """
    try:
        async with get_http_client() as client:
            params = {
                "limit": limit,
                "page": page,
                "order": order,
                "has_breeds": 1 if has_breeds else 0
            }
            
            # Add optional filters
            if breed_ids:
                params["breed_ids"] = breed_ids
            if category_ids:
                params["category_ids"] = category_ids
            if sub_id:
                params["sub_id"] = sub_id
            
            response = await client.get(f"{CAT_API_BASE_URL}/images/search", params=params)
            response.raise_for_status()
            
            images = response.json()
            
            return {
                "success": True,
                "count": len(images),
                "images": images,
                "page": page,
                "limit": limit
            }
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to search cat images: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error searching cat images: {str(e)}")

@mcp.tool()
async def get_cat_image_by_id(
    image_id: Annotated[str, Field(description="Unique ID of the cat image")]
) -> Dict[str, Any]:
    """
    Get a specific cat image by its unique ID.
    
    Returns detailed information about the image including breed data if available.
    """
    try:
        async with get_http_client() as client:
            response = await client.get(f"{CAT_API_BASE_URL}/images/{image_id}")
            response.raise_for_status()
            
            image_data = response.json()
            
            return {
                "success": True,
                "image": image_data
            }
            
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise ToolError(f"Cat image with ID '{image_id}' not found")
        raise ToolError(f"Failed to get cat image: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting cat image: {str(e)}")

@mcp.tool()
async def get_cat_breeds(
    limit: Annotated[int, Field(description="Number of breeds to return (1-100)", ge=1, le=100)] = 10,
    page: Annotated[int, Field(description="Page number for pagination", ge=0)] = 0,
    attach_breed: Annotated[bool, Field(description="Include breed information")] = True
) -> Dict[str, Any]:
    """
    Get information about cat breeds.
    
    Returns a list of cat breeds with detailed information including temperament,
    origin, life span, and other characteristics.
    """
    try:
        async with get_http_client() as client:
            params = {
                "limit": limit,
                "page": page,
                "attach_breed": 1 if attach_breed else 0
            }
            
            response = await client.get(f"{CAT_API_BASE_URL}/breeds", params=params)
            response.raise_for_status()
            
            breeds = response.json()
            
            return {
                "success": True,
                "count": len(breeds),
                "breeds": breeds,
                "page": page,
                "limit": limit
            }
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to get cat breeds: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting cat breeds: {str(e)}")

@mcp.tool()
async def get_breed_by_id(
    breed_id: Annotated[str, Field(description="Unique ID of the cat breed")]
) -> Dict[str, Any]:
    """
    Get detailed information about a specific cat breed by its ID.
    
    Returns comprehensive breed information including characteristics, origin, and traits.
    """
    try:
        async with get_http_client() as client:
            response = await client.get(f"{CAT_API_BASE_URL}/breeds/{breed_id}")
            response.raise_for_status()
            
            breed_data = response.json()
            
            return {
                "success": True,
                "breed": breed_data
            }
            
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise ToolError(f"Cat breed with ID '{breed_id}' not found")
        raise ToolError(f"Failed to get cat breed: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting cat breed: {str(e)}")

@mcp.tool()
async def search_breeds_by_name(
    name: Annotated[str, Field(description="Name or partial name of the breed to search for")]
) -> Dict[str, Any]:
    """
    Search for cat breeds by name.
    
    Returns breeds that match the search query, useful for finding breeds
    when you only know part of the name.
    """
    try:
        # Create client directly instead of using get_http_client() as async context manager
        client = await get_http_client()
        try:
            params = {"q": name}
            response = await client.get(f"{CAT_API_BASE_URL}/breeds/search", params=params)
            response.raise_for_status()
            
            breeds = response.json()
            
            return {
                "success": True,
                "count": len(breeds),
                "breeds": breeds,
                "search_query": name
            }
        finally:
            await client.aclose()
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to search cat breeds: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error searching cat breeds: {str(e)}")

@mcp.tool()
async def get_cat_categories() -> Dict[str, Any]:
    """
    Get all available cat image categories.
    
    Returns a list of categories that can be used to filter cat images.
    """
    try:
        async with get_http_client() as client:
            response = await client.get(f"{CAT_API_BASE_URL}/categories")
            response.raise_for_status()
            
            categories = response.json()
            
            return {
                "success": True,
                "count": len(categories),
                "categories": categories
            }
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to get cat categories: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting cat categories: {str(e)}")

@mcp.tool()
async def get_random_cat_fact() -> Dict[str, Any]:
    """
    Get a random cat fact.
    
    This uses a different API endpoint to provide interesting cat facts.
    Note: This is a bonus feature and may not always be available.
    """
    try:
        # Using a different API for cat facts as The Cat API doesn't have facts
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("https://catfact.ninja/fact")
            response.raise_for_status()
            
            fact_data = response.json()
            
            return {
                "success": True,
                "fact": fact_data.get("fact", "No fact available"),
                "length": fact_data.get("length", 0)
            }
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to get cat fact: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting cat fact: {str(e)}")

@mcp.tool()
async def get_breed_images(
    breed_id: Annotated[str, Field(description="Unique ID of the cat breed")],
    limit: Annotated[int, Field(description="Number of images to return (1-100)", ge=1, le=100)] = 5,
    page: Annotated[int, Field(description="Page number for pagination", ge=0)] = 0
) -> Dict[str, Any]:
    """
    Get images for a specific cat breed.
    
    Returns a collection of images for the specified breed with metadata.
    """
    try:
        async with get_http_client() as client:
            params = {
                "breed_ids": breed_id,
                "limit": limit,
                "page": page,
                "has_breeds": 1
            }
            
            response = await client.get(f"{CAT_API_BASE_URL}/images/search", params=params)
            response.raise_for_status()
            
            images = response.json()
            
            return {
                "success": True,
                "breed_id": breed_id,
                "count": len(images),
                "images": images,
                "page": page,
                "limit": limit
            }
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to get breed images: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting breed images: {str(e)}")

@mcp.tool()
async def get_category_images(
    category_id: Annotated[int, Field(description="ID of the category")],
    limit: Annotated[int, Field(description="Number of images to return (1-100)", ge=1, le=100)] = 5,
    page: Annotated[int, Field(description="Page number for pagination", ge=0)] = 0
) -> Dict[str, Any]:
    """
    Get images for a specific category.
    
    Returns a collection of images for the specified category.
    """
    try:
        async with get_http_client() as client:
            params = {
                "category_ids": str(category_id),
                "limit": limit,
                "page": page
            }
            
            response = await client.get(f"{CAT_API_BASE_URL}/images/search", params=params)
            response.raise_for_status()
            
            images = response.json()
            
            return {
                "success": True,
                "category_id": category_id,
                "count": len(images),
                "images": images,
                "page": page,
                "limit": limit
            }
            
    except httpx.HTTPError as e:
        raise ToolError(f"Failed to get category images: {str(e)}")
    except Exception as e:
        raise ToolError(f"Unexpected error getting category images: {str(e)}")

# Resources for providing API documentation and breed information
@mcp.resource("docs://api-info")
def get_api_info() -> Dict[str, Any]:
    """Provides information about The Cat API and available endpoints."""
    return {
        "title": "The Cat API Information",
        "base_url": CAT_API_BASE_URL,
        "description": "The Cat API provides access to thousands of cat images and breed information",
        "endpoints": {
            "images": "/images/search - Search for cat images",
            "breeds": "/breeds - Get cat breed information",
            "categories": "/categories - Get image categories"
        },
        "authentication": "API key required for full access",
        "rate_limits": "Standard rate limits apply",
        "documentation": "https://docs.thecatapi.com/"
    }

@mcp.resource("docs://breed-list")
async def get_popular_breeds() -> Dict[str, Any]:
    """Provides a list of popular cat breeds with their IDs."""
    popular_breeds = {
        "abys": "Abyssinian",
        "beng": "Bengal",
        "bmbl": "Bombay",
        "bslo": "British Longhair",
        "bsho": "British Shorthair",
        "main": "Maine Coon",
        "pers": "Persian",
        "ragd": "Ragdoll",
        "siam": "Siamese",
        "sphy": "Sphynx"
    }
    
    return {
        "title": "Popular Cat Breeds",
        "description": "Common cat breeds and their IDs for use with the API",
        "breeds": popular_breeds,
        "note": "Use these IDs with the breed_ids parameter in image search"
    }

@mcp.resource("docs://usage-examples")
def get_usage_examples() -> Dict[str, Any]:
    """Provides usage examples for the Cat API tools."""
    return {
        "title": "Cat API Usage Examples",
        "examples": {
            "basic_search": {
                "description": "Get 5 random cat images",
                "tool": "search_cat_images",
                "parameters": {"limit": 5}
            },
            "breed_search": {
                "description": "Get Bengal cat images",
                "tool": "search_cat_images",
                "parameters": {"breed_ids": "beng", "limit": 3}
            },
            "breed_info": {
                "description": "Get information about a specific breed",
                "tool": "get_breed_by_id",
                "parameters": {"breed_id": "beng"}
            },
            "category_search": {
                "description": "Get images from a specific category",
                "tool": "get_category_images",
                "parameters": {"category_id": 1, "limit": 3}
            }
        }
    }

# Error handling and logging
@mcp.tool()
async def health_check() -> Dict[str, Any]:
    """
    Check the health of The Cat API service.
    
    Returns the status of the API and connection information.
    """
    try:
        async with get_http_client() as client:
            response = await client.get(f"{CAT_API_BASE_URL}/images/search?limit=1")
            response.raise_for_status()
            
            return {
                "success": True,
                "status": "healthy",
                "api_accessible": True,
                "response_time_ms": response.elapsed.total_seconds() * 1000,
                "api_key_configured": bool(API_KEY)
            }
            
    except Exception as e:
        return {
            "success": False,
            "status": "unhealthy",
            "api_accessible": False,
            "error": str(e),
            "api_key_configured": bool(API_KEY)
        }

if __name__ == "__main__":
    # Log startup information
    logger.info("Starting The Cat API MCP Server")
    logger.info(f"API Key configured: {'Yes' if API_KEY else 'No'}")
    logger.info(f"Base URL: {CAT_API_BASE_URL}")
    
    # Run the server
    mcp.run()
